#!/usr/bin/env python3
"""
分析LinkedIn职位提取的详细情况
"""

from linkedin_automation import LinkedInAutomation
from pathlib import Path
import json
from bs4 import BeautifulSoup

def analyze_job_sections():
    """分析提取到的职位片段"""
    # 读取保存的HTML快照
    html_file = Path('../log/linkedin_jobs_full_page.html')
    if not html_file.exists():
        print('❌ 未找到HTML快照文件')
        return
    
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 创建自动化实例
    automation = LinkedInAutomation()
    
    # 测试职位片段提取
    print('🔍 分析职位片段提取...')
    job_sections = automation._extract_job_sections(html_content)
    print(f'总共提取到 {len(job_sections)} 个职位片段')
    print()
    
    # 分析每个片段的内容
    for i, section in enumerate(job_sections):
        soup = BeautifulSoup(section, 'html.parser')
        
        # 尝试找到职位标题
        title_selectors = [
            '.job-card-list__title',
            '.base-search-card__title', 
            '.job-card-container__link',
            'h3 a',
            '[data-job-id] h3',
            '.artdeco-entity-lockup__title',
            'a[data-tracking-control-name="public_jobs_jserp-result_search-card"]'
        ]
        
        title = 'N/A'
        for selector in title_selectors:
            title_elem = soup.select_one(selector)
            if title_elem:
                title = title_elem.get_text(strip=True)
                break
        
        # 尝试找到公司名称
        company_selectors = [
            '.job-card-container__primary-description',
            '.base-search-card__subtitle',
            '.job-card-list__company-name',
            'h4 a',
            '.artdeco-entity-lockup__subtitle',
            'a[data-tracking-control-name="public_jobs_jserp-result_job-search-card-subtitle"]'
        ]
        
        company = 'N/A'
        for selector in company_selectors:
            company_elem = soup.select_one(selector)
            if company_elem:
                company = company_elem.get_text(strip=True)
                break
        
        # 尝试找到地点
        location_selectors = [
            '.job-card-container__metadata-item',
            '.job-search-card__location',
            '.artdeco-entity-lockup__caption'
        ]
        
        location = 'N/A'
        for selector in location_selectors:
            location_elem = soup.select_one(selector)
            if location_elem:
                location = location_elem.get_text(strip=True)
                break
        
        print(f'片段 {i+1}: {title}')
        print(f'  公司: {company}')
        print(f'  地点: {location}')
        print(f'  长度: {len(section)} 字符')
        print()
    
    print('=' * 60)
    print('🔍 测试完整LLM解析...')
    jobs = automation._parse_jobs_with_llm(html_content, 'Marketing Manager', 'Shanghai China')
    print(f'LLM解析结果: {len(jobs)} 个职位')
    print()
    
    for i, job in enumerate(jobs):
        title = job.get('title', 'N/A')
        company = job.get('company', 'N/A')
        location = job.get('location', 'N/A')
        url = job.get('url', 'N/A')
        easy_apply = job.get('is_easy_apply', 'N/A')
        
        print(f'职位 {i+1}: {title}')
        print(f'  公司: {company}')
        print(f'  地点: {location}')
        print(f'  URL: {url[:100]}...' if len(str(url)) > 100 else f'  URL: {url}')
        print(f'  Easy Apply: {easy_apply}')
        print()

if __name__ == '__main__':
    analyze_job_sections()
