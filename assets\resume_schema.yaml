# YAML Schema for plain_text_resume.yaml

personal_information:
  type: object
  properties:
    name: {type: string}
    surname: {type: string}
    date_of_birth: {type: string, format: date}
    country: {type: string}
    zip_code: {type: string, pattern: "^[0-9]{5,10}$"}
    city: {type: string}
    address: {type: string}
    phone_prefix: {type: string, format: phone_prefix}
    phone: {type: string, format: phone}
    email: {type: string, format: email}
    github: {type: string, format: uri}
    linkedin: {type: string, format: uri}
  required: [name, surname, date_of_birth, country, city, address, zip_code, phone_prefix, phone, email]

education_details:
  type: array
  items:
    type: object
    properties:
      degree: {type: string}
      university: {type: string}
      gpa: {type: string}
      graduation_year: {type: string}
      field_of_study: {type: string}
      exam:
        type: object
        additionalProperties: {type: string}
    required: [degree, university, gpa, graduation_year, field_of_study]

experience_details:
  type: array
  items:
    type: object
    properties:
      position: {type: string}
      company: {type: string}
      employment_period: {type: string}
      location: {type: string}
      industry: {type: string}
      key_responsibilities:
        type: object
        additionalProperties: {type: string}
      skills_acquired:
        type: array
        items: {type: string}
    required: [position, company, employment_period, location, industry, key_responsibilities, skills_acquired]

projects:
  type: array
  items:
    type: object
    properties:
      name: {type: string}
      description: {type: string}
      link: {type: string, format: uri}
    required: [name, description]

achievements:
  type: array
  items:
    type: object
    properties:
      name: {type: string}
      description: {type: string}
    required: [name, description]

certifications:
  type: array
  items: {type: string}

languages:
  type: array
  items:
    type: object
    properties:
      language: {type: string}
      proficiency: {type: string, enum: [Native, Fluent, Intermediate, Beginner]}
    required: [language, proficiency]

interests:
  type: array
  items: {type: string}

availability:
  type: object
  properties:
    notice_period: {type: string}
  required: [notice_period]

salary_expectations:
  type: object
  properties:
    salary_range_usd: {type: string}
  required: [salary_range_usd]

self_identification:
  type: object
  properties:
    gender: {type: string}
    pronouns: {type: string}
    veteran: {type: string, enum: [Yes, No]}
    disability: {type: string, enum: [Yes, No]}
    ethnicity: {type: string}
  required: [gender, pronouns, veteran, disability, ethnicity]

legal_authorization:
  type: object
  properties:
    eu_work_authorization: {type: string, enum: [Yes, No]}
    us_work_authorization: {type: string, enum: [Yes, No]}
    requires_us_visa: {type: string, enum: [Yes, No]}
    requires_us_sponsorship: {type: string, enum: [Yes, No]}
    requires_eu_visa: {type: string, enum: [Yes, No]}
    legally_allowed_to_work_in_eu: {type: string, enum: [Yes, No]}
    legally_allowed_to_work_in_us: {type: string, enum: [Yes, No]}
    requires_eu_sponsorship: {type: string, enum: [Yes, No]}
  required: [eu_work_authorization, us_work_authorization, requires_us_visa, requires_us_sponsorship, requires_eu_visa, legally_allowed_to_work_in_eu, legally_allowed_to_work_in_us, requires_eu_sponsorship]

work_preferences:
  type: object
  properties:
    remote_work: {type: string, enum: [Yes, No]}
    in_person_work: {type: string, enum: [Yes, No]}
    open_to_relocation: {type: string, enum: [Yes, No]}
    willing_to_complete_assessments: {type: string, enum: [Yes, No]}
    willing_to_undergo_drug_tests: {type: string, enum: [Yes, No]}
    willing_to_undergo_background_checks: {type: string, enum: [Yes, No]}
  required: [remote_work, in_person_work, open_to_relocation, willing_to_complete_assessments, willing_to_undergo_drug_tests, willing_to_undergo_background_checks]
