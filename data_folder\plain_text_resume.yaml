personal_information:
  name: "<PERSON><PERSON>"
  surname: "<PERSON>"
  date_of_birth: "08/19/1977"
  country: "China"
  city: "Shanghai"
  address: "[Your Address]"
  zip_code: "200050"
  phone_prefix: "+86"
  phone: "13916847190"
  email: "<EMAIL>"
  github: null
  linkedin: https://www.linkedin.com/in/huangjacky/

education_details:
  - education_level: "Bachelor's Degree"
    institution: "[Your Institution]"
    field_of_study: "[Your Field of Study]"
    final_evaluation_grade: "[Your Final Evaluation Grade]"
    start_date: "[Start Date]"
    year_of_completion: 2006
    exam:
      exam_name_1: "[Grade]"
      exam_name_2: "[Grade]"
      exam_name_3: "[Grade]"
      exam_name_4: "[Grade]"
      exam_name_5: "[Grade]"
      exam_name_6: "[Grade]"

experience_details:
  - position: "[Your Position]"
    company: "[Company Name]"
    employment_period: "[Employment Period]"
    location: "[Location]"
    industry: "[Industry]"
    key_responsibilities:
      - responsibility_1: "[Responsibility Description]"
      - responsibility_2: "[Responsibility Description]"
      - responsibility_3: "[Responsibility Description]"
    skills_acquired:
      - "[Skill]"
      - "[Skill]"
      - "[Skill]"

  - position: "[Your Position]"
    company: "[Company Name]"
    employment_period: "[Employment Period]"
    location: "[Location]"
    industry: "[Industry]"
    key_responsibilities:
      - responsibility_1: "[Responsibility Description]"
      - responsibility_2: "[Responsibility Description]"
      - responsibility_3: "[Responsibility Description]"
    skills_acquired:
      - "[Skill]"
      - "[Skill]"
      - "[Skill]"

projects:
  - name: "[Project Name]"
    description: "[Project Description]"
    link: null

  - name: "[Project Name]"
    description: "[Project Description]"
    link: null

achievements:
  - name: "[Achievement Name]"
    description: "[Achievement Description]"
  - name: "[Achievement Name]"
    description: "[Achievement Description]"

certifications:
  - name: "[Certification Name]"
    description: "[Certification Description]"
  - name: "[Certification Name]"
    description: "[Certification Description]"

languages:
  - language: "[Language]"
    proficiency: "[Proficiency Level]"
  - language: "[Language]"
    proficiency: "[Proficiency Level]"

interests:
  - "[Interest]"
  - "[Interest]"
  - "[Interest]"

availability:
  notice_period: "[Notice Period]"

salary_expectations:
  salary_range_usd: "[Salary Range]"

self_identification:
  gender: "[Gender]"
  pronouns: "[Pronouns]"
  veteran: "[Yes/No]"
  disability: "[Yes/No]"
  ethnicity: "[Ethnicity]"


legal_authorization:
  eu_work_authorization: "[Yes/No]"
  us_work_authorization: "[Yes/No]"
  requires_us_visa: "[Yes/No]"
  requires_us_sponsorship: "[Yes/No]"
  requires_eu_visa: "[Yes/No]"
  legally_allowed_to_work_in_eu: "[Yes/No]"
  legally_allowed_to_work_in_us: "[Yes/No]"
  requires_eu_sponsorship: "[Yes/No]"
  canada_work_authorization: "[Yes/No]"
  requires_canada_visa: "[Yes/No]"
  legally_allowed_to_work_in_canada: "[Yes/No]"
  requires_canada_sponsorship: "[Yes/No]"
  uk_work_authorization: "[Yes/No]"
  requires_uk_visa: "[Yes/No]"
  legally_allowed_to_work_in_uk: "[Yes/No]"
  requires_uk_sponsorship: "[Yes/No]"


work_preferences:
  remote_work: "[Yes/No]"
  in_person_work: "[Yes/No]"
  open_to_relocation: "[Yes/No]"
  willing_to_complete_assessments: "[Yes/No]"
  willing_to_undergo_drug_tests: "[Yes/No]"
  willing_to_undergo_background_checks: "[Yes/No]"
