#!/usr/bin/env python3
"""
安装和配置Undetected ChromeDriver
"""

import subprocess
import sys
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def install_package(package_name: str) -> bool:
    """安装Python包"""
    try:
        logger.info(f"正在安装 {package_name}...")
        result = subprocess.run(
            [sys.executable, "-m", "pip", "install", package_name],
            capture_output=True,
            text=True,
            check=True
        )
        logger.info(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ 安装 {package_name} 失败:")
        logger.error(f"错误输出: {e.stderr}")
        return False

def check_installation(package_name: str) -> bool:
    """检查包是否已安装"""
    try:
        __import__(package_name.replace('-', '_'))
        logger.info(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        logger.info(f"❌ {package_name} 未安装")
        return False

def test_undetected_chromedriver():
    """测试undetected-chromedriver是否工作正常"""
    try:
        logger.info("测试 undetected-chromedriver...")

        import undetected_chromedriver as uc

        # 创建简单的测试，指定Chrome版本
        options = uc.ChromeOptions()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')

        # 尝试不同的Chrome版本
        chrome_versions = [137, 136, 135, None]  # None表示自动检测

        for version in chrome_versions:
            try:
                logger.info(f"尝试Chrome版本: {version if version else '自动检测'}")

                driver_kwargs = {
                    'options': options,
                    'use_subprocess': True,
                    'keep_alive': False
                }

                if version:
                    driver_kwargs['version_main'] = version

                driver = uc.Chrome(**driver_kwargs)

                # 测试访问一个简单页面
                driver.get('https://httpbin.org/user-agent')

                # 检查用户代理
                page_source = driver.page_source
                logger.info("页面内容预览:")
                logger.info(page_source[:200] + "..." if len(page_source) > 200 else page_source)

                driver.quit()
                logger.info(f"✅ undetected-chromedriver 测试成功 (Chrome {version if version else '自动'})")
                return True

            except Exception as e:
                logger.warning(f"Chrome版本 {version if version else '自动'} 测试失败: {str(e)}")
                try:
                    if 'driver' in locals():
                        driver.quit()
                except:
                    pass
                continue

        logger.error("❌ 所有Chrome版本测试都失败")
        return False

    except Exception as e:
        logger.error(f"❌ undetected-chromedriver 测试失败: {str(e)}")
        return False

def update_requirements():
    """更新requirements.txt文件"""
    try:
        requirements_file = Path("requirements.txt")
        
        if requirements_file.exists():
            # 读取现有内容
            with open(requirements_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否已包含undetected-chromedriver
            if 'undetected-chromedriver' not in content:
                # 添加到文件末尾
                with open(requirements_file, 'a', encoding='utf-8') as f:
                    f.write('\n# 反检测浏览器驱动\n')
                    f.write('undetected-chromedriver>=3.5.0\n')
                
                logger.info("✅ 已更新 requirements.txt")
            else:
                logger.info("✅ requirements.txt 已包含 undetected-chromedriver")
        else:
            # 创建新的requirements.txt
            with open(requirements_file, 'w', encoding='utf-8') as f:
                f.write('# 反检测浏览器驱动\n')
                f.write('undetected-chromedriver>=3.5.0\n')
            
            logger.info("✅ 已创建 requirements.txt")
            
    except Exception as e:
        logger.warning(f"⚠️ 更新 requirements.txt 失败: {str(e)}")

def main():
    """主安装流程"""
    logger.info("🚀 开始安装 Undetected ChromeDriver...")
    
    # 检查是否已安装
    if check_installation('undetected-chromedriver'):
        logger.info("undetected-chromedriver 已安装，跳过安装步骤")
    else:
        # 安装包
        if not install_package('undetected-chromedriver'):
            logger.error("安装失败，请手动运行: pip install undetected-chromedriver")
            return False
    
    # 测试安装
    if test_undetected_chromedriver():
        logger.info("🎉 Undetected ChromeDriver 安装和测试成功！")
        
        # 更新requirements.txt
        update_requirements()
        
        logger.info("\n📋 使用说明:")
        logger.info("1. 在LinkedIn自动化中，系统会自动优先使用Undetected ChromeDriver")
        logger.info("2. 如果遇到问题，系统会自动回退到标准Selenium")
        logger.info("3. 运行 python test_undetected_chromedriver.py 进行性能对比测试")
        
        return True
    else:
        logger.error("❌ 安装验证失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
