# 用于自动检测本地Chrome主版本，极大加速undetected-chromedriver启动
chromedriver-autoinstaller
click
inquirer
git+https://github.com/feder-cr/lib_resume_builder_AIHawk.git
httpx~=0.27.2
inputimeout==1.0.4
jsonschema==4.23.0
jsonschema-specifications==2023.12.1
langchain==0.2.11
langchain-anthropic
langchain-huggingface
langchain-community==0.2.10
langchain-core==0.2.36
langchain-google-genai==1.0.10
langchain-ollama==0.1.3
langchain-openai==0.1.17
langchain-text-splitters==0.2.2
langsmith==0.1.93
Levenshtein==0.25.1
loguru==0.7.2
openai==1.37.1
pdfminer.six==20221105
pytest>=8.3.3
python-dotenv~=1.0.1
PyYAML~=6.0.2
regex==2024.7.24
reportlab==4.2.2
selenium==4.9.1
webdriver-manager==4.0.2
playwright>=1.40.0
beautifulsoup4>=4.12.0
pytest
pytest-mock
pytest-cov
undetected-chromedriver==3.5.5
pyppeteer==1.0.2
aiofiles==23.2.1