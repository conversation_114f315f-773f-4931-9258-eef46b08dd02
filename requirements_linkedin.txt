# LinkedIn自动化所需的额外依赖包

# Selenium WebDriver
selenium>=4.15.0

# Playwright (推荐的浏览器自动化工具)
playwright>=1.40.0

# HTML解析
beautifulsoup4>=4.12.0

# 日志记录
loguru>=0.7.2

# YAML配置文件处理
PyYAML>=6.0.1

# 异步支持
aiofiles>=23.2.0

# HTTP客户端
httpx>=0.25.0

# 数据验证
pydantic>=2.5.0

# FastAPI相关
fastapi>=0.104.0
uvicorn>=0.24.0

# 随机延迟和人类行为模拟
random-user-agent>=1.0.1

# Chrome WebDriver管理
webdriver-manager>=4.0.1

# 数据处理
pandas>=2.1.0
numpy>=1.24.0

# 时间处理
python-dateutil>=2.8.2

# 环境变量管理
python-dotenv>=1.0.0

# 数据库支持（可选，用于存储申请记录）
sqlalchemy>=2.0.0
sqlite3  # 内置模块

# 图像处理（可选，用于验证码处理）
Pillow>=10.0.0

# 机器学习（可选，用于智能问题回答）
scikit-learn>=1.3.0

# 网络请求
requests>=2.31.0

# 文件路径处理
pathlib  # 内置模块

# 正则表达式
re  # 内置模块

# JSON处理
json  # 内置模块

# 时间处理
time  # 内置模块

# 系统相关
os  # 内置模块
sys  # 内置模块

# 类型提示
typing  # 内置模块