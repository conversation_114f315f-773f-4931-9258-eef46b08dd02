"""
This module is responsible for generating tailored resumes based on user's existing resume and job description.
"""

from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.schema import HumanMessage
from loguru import logger
import config as cfg


class LLMTailoredResumeGenerator:
    def __init__(self, api_key: str, strings):
        self.api_key = api_key
        self.strings = strings
        self.llm = ChatGoogleGenerativeAI(
            model=cfg.LLM_MODEL,
            google_api_key=api_key,
            temperature=1.0,
            transport="rest"
        )
        self.user_resume_data = None
        self.job_description = None
        self.resume_object = None

    def set_user_resume_data(self, user_resume_data: dict):
        """设置用户原始简历数据"""
        self.user_resume_data = user_resume_data
        logger.info("用户简历数据已设置")

    def set_job_description_from_text(self, job_description: str):
        """设置职位描述"""
        self.job_description = job_description
        logger.info("职位描述已设置")

    def set_resume(self, resume_object):
        """设置简历对象"""
        self.resume_object = resume_object
        logger.info("简历对象已设置")

    def generate_html_resume(self) -> str:
        """
        基于用户原始简历和职位描述生成针对性优化的HTML简历
        """
        if not self.user_resume_data:
            logger.warning("用户简历数据未设置，使用默认简历生成")
            return self._generate_default_resume()

        if not self.job_description:
            logger.warning("职位描述未设置，使用原始简历")
            return self._generate_from_user_data()

        logger.info("开始生成针对性优化简历")
        return self._generate_tailored_resume()

    def _generate_tailored_resume(self) -> str:
        """生成针对性优化简历"""
        try:
            # 构建优化提示词
            prompt = f"""
            作为专业的简历优化专家，请基于用户的原始简历和目标职位描述，生成一份针对性优化的HTML简历。

            用户原始简历信息：
            {self._format_user_resume_data()}

            目标职位描述：
            {self.job_description}

            请按照以下要求优化简历：

            1. **技能匹配优化**：
               - 突出与职位要求最匹配的技能
               - 调整技能描述的顺序和重点
               - 添加职位相关的关键词

            2. **工作经验优化**：
               - 重新组织工作经验的描述，突出与目标职位相关的成就
               - 使用与职位描述相似的动词和术语
               - 量化成果和影响力

            3. **项目经验优化**：
               - 优先展示与目标职位技术栈匹配的项目
               - 调整项目描述，突出相关技术和成果

            4. **教育背景优化**：
               - 突出与职位相关的课程和成绩
               - 如果相关，添加学术项目

            请生成完整的HTML简历内容，包含以下部分：
            - 个人信息和联系方式
            - 专业技能（按相关性排序）
            - 工作经验（突出相关成就）
            - 项目经验（优先展示相关项目）
            - 教育背景
            - 证书和语言能力（如有）

            HTML格式要求：
            - 使用语义化的HTML标签
            - 包含适当的CSS类名用于样式设置
            - 结构清晰，易于阅读
            - 不要包含<html>, <head>, <body>标签，只返回简历内容部分

            重要注意事项：
            - 请确保优化后的简历既保持真实性，又最大化地匹配目标职位要求
            - 不要在简历内容中使用"N/A"、"无"等占位符
            - 如果某些信息不可用，请直接省略该部分，不要显示空值
            - 确保所有显示的信息都是有意义和有价值的
            """

            # 调用LLM生成优化简历
            response = self.llm.invoke([HumanMessage(content=prompt)])
            html_content = response.content

            logger.info("针对性简历生成成功")
            return html_content

        except Exception as e:
            logger.error(f"生成针对性简历时发生错误: {e}")
            # 降级到使用用户数据生成
            return self._generate_from_user_data()

    def _generate_from_user_data(self) -> str:
        """基于用户数据生成简历"""
        try:
            prompt = f"""
            请基于以下用户简历信息生成一份专业的HTML简历：

            {self._format_user_resume_data()}

            请生成完整的HTML简历内容，包含：
            - 个人信息和联系方式
            - 专业技能
            - 工作经验
            - 项目经验
            - 教育背景
            - 证书和语言能力

            HTML格式要求：
            - 使用语义化的HTML标签
            - 包含适当的CSS类名
            - 结构清晰，易于阅读
            - 不要包含<html>, <head>, <body>标签

            重要注意事项：
            - 不要在简历内容中使用"N/A"、"无"等占位符
            - 如果某些信息不可用，请直接省略该部分
            - 确保所有显示的信息都是有意义和有价值的
            """

            response = self.llm.invoke([HumanMessage(content=prompt)])
            return response.content

        except Exception as e:
            logger.error(f"基于用户数据生成简历时发生错误: {e}")
            return self._generate_default_resume()

    def _generate_default_resume(self) -> str:
        """生成默认简历（使用原始简历对象）"""
        if self.resume_object:
            # 使用原始简历对象生成基础HTML
            return f"""
            <div class="resume-container">
                <header class="resume-header">
                    <h1>{getattr(self.resume_object, 'name', '姓名')}</h1>
                    <div class="contact-info">
                        <p>邮箱: {getattr(self.resume_object, 'email', '邮箱地址')}</p>
                        <p>电话: {getattr(self.resume_object, 'phone', '电话号码')}</p>
                    </div>
                </header>
                <section class="resume-section">
                    <h2>个人简介</h2>
                    <p>专业的求职者，具有丰富的工作经验和技能。</p>
                </section>
            </div>
            """
        else:
            return """
            <div class="resume-container">
                <header class="resume-header">
                    <h1>求职者姓名</h1>
                    <div class="contact-info">
                        <p>邮箱: <EMAIL></p>
                        <p>电话: ************</p>
                    </div>
                </header>
                <section class="resume-section">
                    <h2>个人简介</h2>
                    <p>专业的求职者，寻求新的职业机会。</p>
                </section>
            </div>
            """

    def _format_user_resume_data(self) -> str:
        """格式化用户简历数据为可读文本"""
        if not self.user_resume_data:
            return "无用户简历数据"

        formatted_text = []

        # 个人信息
        personal_info = self.user_resume_data.get('personal_info', {})
        if personal_info:
            formatted_text.append("=== 个人信息 ===")
            for key, value in personal_info.items():
                if value:
                    formatted_text.append(f"{key}: {value}")

        # 教育背景
        education = self.user_resume_data.get('education', [])
        if education:
            formatted_text.append("\n=== 教育背景 ===")
            for edu in education:
                # 兼容多种字段名格式
                school = edu.get('school') or edu.get('institution') or edu.get('university', '')
                major = edu.get('major') or edu.get('field_of_study') or edu.get('degree', '')
                degree = edu.get('degree') or edu.get('education_level', '')
                graduation_date = edu.get('graduation_date') or edu.get('year_of_completion') or edu.get('graduation_year', '')

                if school:
                    formatted_text.append(f"学校: {school}")
                if major:
                    formatted_text.append(f"专业: {major}")
                if degree:
                    formatted_text.append(f"学位: {degree}")
                if graduation_date:
                    formatted_text.append(f"毕业时间: {graduation_date}")
                formatted_text.append("")

        # 工作经验
        work_exp = self.user_resume_data.get('work_experience', [])
        if work_exp:
            formatted_text.append("=== 工作经验 ===")
            for work in work_exp:
                # 兼容多种字段名格式
                company = work.get('company', '')
                position = work.get('position', '')
                duration = work.get('duration') or work.get('employment_period', '')

                # 处理工作描述 - 可能是字符串或责任列表
                description = work.get('description', '')
                if not description:
                    # 尝试从key_responsibilities获取
                    responsibilities = work.get('key_responsibilities', [])
                    if responsibilities:
                        if isinstance(responsibilities, list):
                            # 处理责任列表
                            desc_parts = []
                            for resp in responsibilities:
                                if isinstance(resp, dict):
                                    # 如果是字典格式，提取值
                                    for key, value in resp.items():
                                        if value:
                                            desc_parts.append(str(value))
                                elif isinstance(resp, str):
                                    desc_parts.append(resp)
                            description = '; '.join(desc_parts)
                        else:
                            description = str(responsibilities)

                if company:
                    formatted_text.append(f"公司: {company}")
                if position:
                    formatted_text.append(f"职位: {position}")
                if duration:
                    formatted_text.append(f"工作时间: {duration}")
                if description:
                    formatted_text.append(f"工作描述: {description}")

                # 添加技能信息
                skills_acquired = work.get('skills_acquired', [])
                if skills_acquired:
                    formatted_text.append(f"获得技能: {', '.join(skills_acquired)}")

                formatted_text.append("")

        # 技能
        skills = self.user_resume_data.get('skills', {})
        if skills:
            formatted_text.append("=== 技能 ===")
            for skill_type, skill_list in skills.items():
                if skill_list:
                    if isinstance(skill_list, list):
                        formatted_text.append(f"{skill_type}: {', '.join(skill_list)}")
                    else:
                        formatted_text.append(f"{skill_type}: {skill_list}")

        # 项目经验
        projects = self.user_resume_data.get('projects', [])
        if projects:
            formatted_text.append("\n=== 项目经验 ===")
            for project in projects:
                name = project.get('name', '')
                description = project.get('description', '')
                technologies = project.get('technologies', [])

                if name:
                    formatted_text.append(f"项目名称: {name}")
                if description:
                    formatted_text.append(f"项目描述: {description}")
                if technologies:
                    if isinstance(technologies, list):
                        formatted_text.append(f"使用技术: {', '.join(technologies)}")
                    else:
                        formatted_text.append(f"使用技术: {technologies}")
                formatted_text.append("")

        # 证书
        certifications = self.user_resume_data.get('certifications', [])
        if certifications:
            formatted_text.append("=== 证书 ===")
            for cert in certifications:
                name = cert.get('name', '')
                description = cert.get('description', '')
                if name:
                    formatted_text.append(f"证书名称: {name}")
                if description:
                    formatted_text.append(f"证书描述: {description}")
                formatted_text.append("")

        # 语言能力
        languages = self.user_resume_data.get('languages', [])
        if languages:
            formatted_text.append("=== 语言能力 ===")
            for lang in languages:
                language = lang.get('language', '')
                proficiency = lang.get('proficiency', '')
                if language:
                    lang_text = f"语言: {language}"
                    if proficiency:
                        lang_text += f" (水平: {proficiency})"
                    formatted_text.append(lang_text)

        return "\n".join(formatted_text)
