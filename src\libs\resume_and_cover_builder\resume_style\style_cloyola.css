/*Cloyola Grey $https://github.com/cloyola*/
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');

body {
  font-family: 'Roboto', sans-serif;
  line-height: 1.4;
  color: #333;
  max-width: 700px;
  margin: 0 auto;
  padding: 10px;
  font-size: 9pt;
}

header {
    text-align: left;
    margin-bottom: 20px;
    background-color: #7c7c7c40;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

h1 {
  font-size: 18pt;
  font-weight: 700;
  margin: 0 0 5px 0;
}

.contact-info {
  display: flex;
  justify-content: left;
  flex-wrap: wrap;
  gap: 10px;
  font-size: 9pt;
  font-weight: normal;
}

.contact-info p {
  margin: 0;
}

.contact-info a {
  color: #0077b5;
  text-decoration: none;
}

.fab,
.fas {
  margin-right: 3px;
}

span.entry-location {
  font-weight: normal;
}

h2 {
  font-size: 14pt;
  font-weight: 600;
  border-bottom: 1px dotted #4c4c4c;
  padding-bottom: 2px;
  margin: 10px 0 5px 0;
  text-align: left;
}

.entry {
    margin-bottom: 15px; /*margin-bottom: 8px;*/
    background-color: #fff;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 3px 3px 5px 2px rgba(0, 0, 0, 0.2);
}

.entry-header {
  display: flex;
  justify-content: space-between;
  font-weight: 600;
  font-size: 10pt;
}

.entry-details {
  display: flex;
  justify-content: space-between;
  font-style: italic;
  margin-bottom: 2px;
  font-size: 9pt;
}

.compact-list {
  margin: 2px 0;
  padding-left: 15px;
}

.compact-list li {
  margin-bottom: 2px;
}

.two-column {
  display: flex;
  justify-content: space-between;
}

.two-column ul {
  width: 48%;
  margin: 0;
  padding-left: 15px;
  list-style-type: circle;
}

a {
  color: #0077b5;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

@media print {
  body {
    padding: 0;
    margin: 0;
    font-size: 9pt;
  }

  @page {
    margin: 0.5cm;
  }

  h1 {
    font-size: 18pt;
  }

  h2 {
    font-size: 11pt;
  }

  .contact-info {
    font-size: 8pt;
  }

  .entry-details {
    font-size: 7pt;
  }

  .compact-list {
    padding-left: 12px;
  }
}
