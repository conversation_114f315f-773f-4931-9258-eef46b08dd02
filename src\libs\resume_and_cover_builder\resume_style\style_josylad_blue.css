/*Modern Blue$https://github.com/josylad*/

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap');

body {
  font-family: 'Poppins', sans-serif;
  line-height: 1.6;
  color: #2c3e50;
  max-width: 850px;
  margin: 0 auto;
  padding: 20px;
  font-size: 10pt;
  background-color: #f9f9f9;
}

header {
  text-align: center;
  margin-bottom: 20px;
  background-color: #3498db;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

h1 {
  font-size: 28pt;
  font-weight: 700;
  margin: 0 0 10px 0;
  color: #fff;
}

.contact-info {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 15px;
  font-size: 10pt;
  font-weight: 300;
  color: #ecf0f1;
}

.contact-info p {
  margin: 0;
}

.contact-info a {
  color: #ecf0f1;
  text-decoration: none;
  transition: color 0.3s ease;
}

.contact-info a:hover {
  color: #2c3e50;
}

.fab,
.fas {
  margin-right: 5px;
}

h2 {
  font-size: 18pt;
  font-weight: 600;
  border-bottom: 2px solid #3498db;
  padding-bottom: 5px;
  margin: 20px 0 15px 0;
  color: #2c3e50;
}

.entry {
  margin-bottom: 15px;
  background-color: #fff;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.entry-header {
  display: flex;
  justify-content: space-between;
  font-weight: 600;
  color: #3498db;
}

.entry-details {
  display: flex;
  justify-content: space-between;
  font-style: italic;
  margin-bottom: 8px;
  font-size: 9pt;
  color: #7f8c8d;
}

.compact-list {
  margin: 5px 0;
  padding-left: 20px;
}

.compact-list li {
  margin-bottom: 5px;
}

.two-column {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.two-column ul {
  width: 48%;
  margin: 0;
  padding-left: 20px;
}

a {
  color: #3498db;
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: #2980b9;
  text-decoration: underline;
}

@media print {
  body {
    padding: 0;
    margin: 0;
    font-size: 9pt;
    background-color: #fff;
  }

  @page {
    margin: 1cm;
  }

  h1 {
    font-size: 24pt;
  }

  h2 {
    font-size: 16pt;
  }

  .contact-info {
    font-size: 9pt;
  }

  .entry-details {
    font-size: 8pt;
  }

  .compact-list {
    padding-left: 15px;
  }

  header {
    box-shadow: none;
  }

  .entry {
    box-shadow: none;
    padding: 10px 0;
  }
}