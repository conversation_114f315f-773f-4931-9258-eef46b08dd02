/*Default$https://github.com/krishnavalliappan*/
body {
  font-family: "<PERSON>", <PERSON><PERSON>, sans-serif;
  line-height: 1.2;
  color: #333;
  max-width: 700px;
  margin: 0 auto;
  padding: 10px;
  font-size: 9pt;
}

header {
  text-align: center;
  margin-bottom: 10px;
}

h1 {
  font-size: 24pt;
  font-weight: 700;
  margin: 0 0 5px 0;
}

.contact-info {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 10px;
  font-size: 9pt;
  font-weight: normal;
}

.contact-info p {
  margin: 0;
}

.contact-info a {
  color: #0077b5;
  text-decoration: none;
}

.fab,
.fas {
  margin-right: 3px;
}

span {
  font-weight: normal;
}

h2 {
  font-size: 16pt;
  font-weight: 600;
  border-bottom: 1px solid #333;
  padding-bottom: 2px;
  margin: 10px 0 5px 0;
  text-align: center;
}

.entry {
  margin-bottom: 8px;
}

.entry-header {
  display: flex;
  justify-content: space-between;
  font-weight: 600;
}

.entry-details {
  display: flex;
  justify-content: space-between;
  font-style: italic;
  margin-bottom: 2px;
  font-size: 8pt;
}

.compact-list {
  margin: 2px 0;
  padding-left: 15px;
}

.compact-list li {
  margin-bottom: 2px;
}

.two-column {
  display: flex;
  justify-content: space-between;
}

.two-column ul {
  width: 48%;
  margin: 0;
  padding-left: 15px;
}

a {
  color: #0077b5;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

@media print {
  body {
    padding: 0;
    margin: 0;
    font-size: 9pt;
  }

  @page {
    margin: 0.5cm;
  }

  h1 {
    font-size: 18pt;
  }

  h2 {
    font-size: 11pt;
  }

  .contact-info {
    font-size: 8pt;
  }

  .entry-details {
    font-size: 7pt;
  }

  .compact-list {
    padding-left: 12px;
  }
}
