/*Clean Blue$https://github.com/samodum*/
@import url("https://fonts.googleapis.com/css2?family=<PERSON>in+Sans&family=<PERSON><PERSON>+<PERSON>runoUmi&family=Open+Sans:ital,wght@0,400;0,600;1,400&display=swap");

:root {
  --pageWidth: 49.62rem;
  --textColor: #383838;
  --lineColorA: #b8b8b8;
  --accentColor: blue;
  --HFont: "Josefin Sans", sans-serif;
  --PFont: "Open Sans", sans-serif;
  --BText: "<PERSON><PERSON> HarunoUmi", serif;
  --sectionSpacing: 1.5rem;
  --bodyFontSize: 0.875rem;
  --KeyColumn: 9.375rem;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  color: var(--textColor);
  font-size: var(--bodyFontSize);
}

body {
  /* border: 1px solid var(--accentColor); page guidelines*/
  max-width: var(--pageWidth);
  padding: 3.375rem 1.5rem;
  display: flex;
  font-family: var(--PFont);
  flex-direction: column;
  gap: 1.5rem;
  margin: 0 auto;
}

main {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  order: 2;
}

a {
  text-decoration: none;
}

a:hover {
  color: var(--accentColor);
  transition: color 0.3s ease;
}

header {
  order: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: start;
  gap: 1.5rem;
}

h1 {
  font-family: var(--HFont);
  font-size: 1.5rem;
  font-weight: 400;
  margin-bottom: -0.125rem;
  color: var(--accentColor);
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.contact-info p {
  font-family: var(--PFont);
}

.contact-info p::before {
  margin-right: 0.25rem;
  text-transform: capitalize;
  font-family: var(--HFont);
  font-weight: 600;
}

.contact-info p:nth-child(1)::before {
  content: "address:";
}
.contact-info p:nth-child(2)::before {
  content: "phone:";
}
.contact-info p:nth-child(3)::before {
  content: "email:";
}
.contact-info p:nth-child(4)::before {
  content: "linkedin:";
}
.contact-info p:nth-child(5)::before {
  content: "github:";
}

section h2 {
  font-family: var(--HFont);
  font-size: 1.125rem;
  font-weight: bold;
  color: var(--accentColor);
  padding-bottom: 0.25rem;
  margin-bottom: 0.5rem;
  border-bottom: 1px solid var(--lineColorA);
}

.entry {
  padding-top: 1rem;
  display: grid;
  grid-template-columns: 1fr 4fr;
  column-gap: 10px;
}

.entry:first-of-type {
  padding-top: 0.5rem;
}

.entry-header {
  grid-column: 1;
  font-family: var(--HFont);
  font-weight: 600;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.entry-details,
#side-projects .compact-list {
  margin-top: -4px;
}

.entry-details,
.compact-list {
  grid-column: 2;
}

.entry-title {
  font-family: var(--HFont);
  font-weight: 600;
  margin-right: 0.25rem;
}

.entry-year {
  font-style: italic;
}

.compact-list {
  padding-left: 10px;
  list-style-type: circle;
  margin: 0;
}

.compact-list li {
  margin-left: 5px;
}

#achievements .compact-list {
  padding-top: 0.25rem;
}

.two-column {
  padding-top: 0.25rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  column-gap: 10px;
}

.two-column .compact-list:first-child {
  grid-column: 1;
}

#work-experience {
  order: 1;
}
#education {
  order: 2;
}
#achievements {
  order: 3;
}
#side-projects {
  order: 4;
}
#skills-languages {
  order: 5;
}
