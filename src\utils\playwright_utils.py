"""Playwright浏览器工具模块
提供Playwright浏览器初始化和PDF生成功能
"""

import os
import time
import base64
from typing import Dict, Optional, Union, Tuple
from playwright.sync_api import sync_playwright, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext
from src.logger_config import logger

def playwright_browser_options(headless: bool = False) -> Dict:
    """设置Playwright浏览器选项
    
    Args:
        headless: 是否使用无头模式
        
    Returns:
        包含浏览器选项的字典
    """
    logger.debug("设置Playwright浏览器选项")
    
    options = {
        "headless": headless,
        "viewport": {"width": 1200, "height": 800},
        "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "ignore_https_errors": True,
    }
    
    logger.debug(f"Playwright浏览器选项设置完成: {options}")
    return options

def init_browser(headless: bool = False) -> <PERSON><PERSON>[<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>onte<PERSON><PERSON>, <PERSON>]:
    """初始化Playwright浏览器
    
    Args:
        headless: 是否使用无头模式
        
    Returns:
        包含Browser, BrowserContext和Page的元组
        
    Raises:
        RuntimeError: 初始化浏览器失败时抛出
    """
    try:
        logger.info("尝试初始化Playwright浏览器...")
        options = playwright_browser_options(headless=headless)
        
        # 启动Playwright
        playwright = sync_playwright().start()
        
        # 启动浏览器 (使用chromium以保持与原selenium chrome相似的行为)
        browser = playwright.chromium.launch(
            headless=options["headless"],
            ignore_https_errors=options["ignore_https_errors"]
        )
        
        # 创建上下文
        context = browser.new_context(
            viewport=options["viewport"],
            user_agent=options["user_agent"]
        )
        
        # 创建页面
        page = context.new_page()
        
        # 设置默认超时
        page.set_default_timeout(30000)  # 30秒
        
        logger.info("Playwright浏览器初始化成功")
        return browser, context, page
    except Exception as e:
        logger.error(f"初始化浏览器失败: {str(e)}", exc_info=True)
        raise RuntimeError(f"初始化浏览器失败: {str(e)}")

def HTML_to_PDF(html_content: str, page: Page) -> str:
    """将HTML内容转换为PDF并返回base64编码的字符串
    
    Args:
        html_content: 要转换的HTML内容
        page: Playwright页面对象
        
    Returns:
        base64编码的PDF内容
        
    Raises:
        ValueError: HTML内容无效时抛出
        RuntimeError: 转换过程中出错时抛出
    """
    # 验证HTML内容
    if not isinstance(html_content, str) or not html_content.strip():
        raise ValueError("HTML内容必须是非空字符串")
    
    try:
        # 设置HTML内容
        page.set_content(html_content, wait_until="networkidle")
        
        # 等待页面完全加载
        page.wait_for_load_state("networkidle")
        
        # 生成PDF
        pdf_buffer = page.pdf(
            format="A4",
            print_background=True,
            margin={
                "top": "0.8in",
                "bottom": "0.8in",
                "left": "0.5in",
                "right": "0.5in"
            }
        )
        
        # 转换为base64
        pdf_base64 = base64.b64encode(pdf_buffer).decode("utf-8")
        return pdf_base64
    except Exception as e:
        logger.error(f"生成PDF时发生错误: {str(e)}")
        raise RuntimeError(f"生成PDF时发生错误: {str(e)}")

def close_browser(browser: Browser, context: Optional[BrowserContext] = None):
    """关闭Playwright浏览器
    
    Args:
        browser: Playwright浏览器对象
        context: 可选的浏览器上下文对象
    """
    try:
        if context:
            context.close()
        browser.close()
        logger.info("Playwright浏览器已关闭")
    except Exception as e:
        logger.error(f"关闭浏览器时发生错误: {str(e)}")