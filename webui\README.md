# WebUI Development

This directory contains the web user interface components for AIHawk.

## Structure

```
webui/
├── backend/    # FastAPI backend API
├── frontend/   # React frontend application
└── README.md   # This file
```

## Development Setup

### Backend
```bash
cd webui/backend
python main.py
```
Runs on: http://localhost:8003

### Frontend
```bash
cd webui/frontend
npm install
npm start
```
Runs on: http://localhost:3000

## Key Features
- Resume generation and optimization
- Cover letter creation
- LinkedIn automation interface
- Real-time job search and application tracking

For detailed setup instructions, see the main [README.md](../README.md) in the project root.