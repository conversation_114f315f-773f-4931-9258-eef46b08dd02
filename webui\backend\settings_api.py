"""
设置管理API模块
提供用户设置的读取、保存、导出、导入等功能
"""

import os
import json
import yaml
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, HTTPException, UploadFile, File
from pydantic import BaseModel
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/settings", tags=["settings"])

# 数据文件夹路径
DATA_FOLDER = Path(__file__).resolve().parents[2] / "data_folder"
SETTINGS_FILE = DATA_FOLDER / "user_settings.yaml"
BACKUP_FOLDER = DATA_FOLDER / "backups"

# 确保目录存在
DATA_FOLDER.mkdir(exist_ok=True)
BACKUP_FOLDER.mkdir(exist_ok=True)

# Pydantic模型
class SettingsData(BaseModel):
    linkedin: Optional[Dict[str, Any]] = None
    data: Optional[Dict[str, Any]] = None
    system: Optional[Dict[str, Any]] = None
    advanced: Optional[Dict[str, Any]] = None

class BackupInfo(BaseModel):
    filename: str
    created_at: str
    size: int
    description: Optional[str] = None

class ExportRequest(BaseModel):
    format: str = "json"  # json, yaml, csv
    sections: Optional[List[str]] = None  # 要导出的设置部分

# 默认设置
DEFAULT_SETTINGS = {
    "linkedin": {
        "automation_type": "selenium",
        "browser": {
            "headless": False,
            "timeout": 30000,
            "retry_count": 3,
            "window_size": [1920, 1080]
        },
        "application": {
            "max_applications_per_day": 50,
            "delay_between_applications": [30, 60],
            "auto_answer_questions": True,
            "default_answers": {
                "years_experience": "3",
                "willing_to_relocate": "Yes",
                "authorized_to_work": "Yes",
                "require_sponsorship": "No"
            }
        },
        "templates": {
            "cover_letter_template": "",
            "follow_up_message": "",
            "thank_you_message": ""
        }
    },
    "data": {
        "auto_backup": True,
        "backup_frequency": "daily",
        "max_backup_files": 10,
        "export_format": "json",
        "clear_history_days": 30
    },
    "system": {
        "theme": "dark",
        "language": "zh",
        "font_size": "medium",
        "notifications": {
            "desktop": True,
            "sound": True,
            "email": False
        },
        "auto_save": True,
        "debug_mode": False
    },
    "advanced": {
        "ai": {
            "model_type": "gemini",
            "model_name": "gemini-2.5-flash",
            "temperature": 1.0,
            "creativity_level": "balanced",
            "optimization_strength": "medium"
        },
        "batch": {
            "max_concurrent_jobs": 3,
            "batch_size": 10,
            "auto_retry_failed": True,
            "retry_delay": 5000
        },
        "performance": {
            "cache_enabled": True,
            "cache_duration": 3600000,
            "preload_data": True,
            "lazy_loading": True
        }
    }
}

def load_settings() -> Dict[str, Any]:
    """加载用户设置"""
    try:
        if SETTINGS_FILE.exists():
            with open(SETTINGS_FILE, 'r', encoding='utf-8') as f:
                settings = yaml.safe_load(f) or {}
                # 合并默认设置，确保所有必要的键都存在
                return merge_settings(DEFAULT_SETTINGS, settings)
        else:
            return DEFAULT_SETTINGS.copy()
    except Exception as e:
        logger.error(f"加载设置失败: {e}")
        return DEFAULT_SETTINGS.copy()

def merge_settings(default: Dict[str, Any], user: Dict[str, Any]) -> Dict[str, Any]:
    """递归合并设置，保留用户设置，补充默认设置"""
    result = default.copy()
    
    for key, value in user.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = merge_settings(result[key], value)
        else:
            result[key] = value
    
    return result

def save_settings(settings: Dict[str, Any]) -> bool:
    """保存用户设置"""
    try:
        # 创建备份
        if SETTINGS_FILE.exists():
            create_backup("auto_backup_before_save")
        
        with open(SETTINGS_FILE, 'w', encoding='utf-8') as f:
            yaml.dump(settings, f, default_flow_style=False, allow_unicode=True)
        
        logger.info("设置保存成功")
        return True
    except Exception as e:
        logger.error(f"保存设置失败: {e}")
        return False

def create_backup(description: str = "") -> str:
    """创建设置备份"""
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"settings_backup_{timestamp}.yaml"
        backup_path = BACKUP_FOLDER / backup_filename
        
        if SETTINGS_FILE.exists():
            shutil.copy2(SETTINGS_FILE, backup_path)
            
            # 创建备份信息文件
            backup_info = {
                "created_at": datetime.now().isoformat(),
                "description": description,
                "original_file": str(SETTINGS_FILE),
                "backup_file": str(backup_path)
            }
            
            info_path = BACKUP_FOLDER / f"settings_backup_{timestamp}.json"
            with open(info_path, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, indent=2, ensure_ascii=False)
            
            # 清理旧备份
            cleanup_old_backups()
            
            logger.info(f"备份创建成功: {backup_filename}")
            return backup_filename
        else:
            logger.warning("设置文件不存在，无法创建备份")
            return ""
    except Exception as e:
        logger.error(f"创建备份失败: {e}")
        return ""

def cleanup_old_backups():
    """清理旧备份文件"""
    try:
        settings = load_settings()
        max_backups = settings.get("data", {}).get("max_backup_files", 10)
        
        # 获取所有备份文件
        backup_files = list(BACKUP_FOLDER.glob("settings_backup_*.yaml"))
        backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        # 删除超出限制的备份文件
        for backup_file in backup_files[max_backups:]:
            try:
                backup_file.unlink()
                # 同时删除对应的信息文件
                info_file = backup_file.with_suffix('.json')
                if info_file.exists():
                    info_file.unlink()
                logger.info(f"删除旧备份: {backup_file.name}")
            except Exception as e:
                logger.error(f"删除备份文件失败: {e}")
    except Exception as e:
        logger.error(f"清理备份失败: {e}")

# API端点
@router.get("/load")
async def load_user_settings():
    """加载用户设置"""
    try:
        settings = load_settings()
        return settings
    except Exception as e:
        logger.error(f"加载设置API失败: {e}")
        raise HTTPException(status_code=500, detail=f"加载设置失败: {str(e)}")

@router.post("/save")
async def save_user_settings(settings: SettingsData):
    """保存用户设置"""
    try:
        settings_dict = settings.model_dump(exclude_none=True)
        
        # 加载现有设置并合并
        current_settings = load_settings()
        updated_settings = merge_settings(current_settings, settings_dict)
        
        success = save_settings(updated_settings)
        if success:
            return {"success": True, "message": "设置保存成功"}
        else:
            raise HTTPException(status_code=500, detail="设置保存失败")
    except Exception as e:
        logger.error(f"保存设置API失败: {e}")
        raise HTTPException(status_code=500, detail=f"保存设置失败: {str(e)}")

@router.post("/backup")
async def create_settings_backup(description: str = ""):
    """创建设置备份"""
    try:
        backup_filename = create_backup(description)
        if backup_filename:
            return {"success": True, "backup_filename": backup_filename, "message": "备份创建成功"}
        else:
            raise HTTPException(status_code=500, detail="备份创建失败")
    except Exception as e:
        logger.error(f"创建备份API失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建备份失败: {str(e)}")

@router.get("/backups", response_model=List[BackupInfo])
async def list_backups():
    """获取备份列表"""
    try:
        backups = []
        backup_files = list(BACKUP_FOLDER.glob("settings_backup_*.yaml"))
        
        for backup_file in backup_files:
            info_file = backup_file.with_suffix('.json')
            backup_info = {
                "filename": backup_file.name,
                "created_at": datetime.fromtimestamp(backup_file.stat().st_mtime).isoformat(),
                "size": backup_file.stat().st_size,
                "description": ""
            }
            
            # 尝试读取备份信息
            if info_file.exists():
                try:
                    with open(info_file, 'r', encoding='utf-8') as f:
                        info_data = json.load(f)
                        backup_info.update(info_data)
                except Exception:
                    pass
            
            backups.append(BackupInfo(**backup_info))
        
        # 按创建时间排序
        backups.sort(key=lambda x: x.created_at, reverse=True)
        return backups
    except Exception as e:
        logger.error(f"获取备份列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取备份列表失败: {str(e)}")

@router.post("/restore/{backup_filename}")
async def restore_backup(backup_filename: str):
    """恢复备份"""
    try:
        backup_path = BACKUP_FOLDER / backup_filename
        if not backup_path.exists():
            raise HTTPException(status_code=404, detail="备份文件不存在")
        
        # 创建当前设置的备份
        create_backup("auto_backup_before_restore")
        
        # 恢复备份
        shutil.copy2(backup_path, SETTINGS_FILE)
        
        return {"success": True, "message": "备份恢复成功"}
    except Exception as e:
        logger.error(f"恢复备份失败: {e}")
        raise HTTPException(status_code=500, detail=f"恢复备份失败: {str(e)}")

@router.delete("/backups/{backup_filename}")
async def delete_backup(backup_filename: str):
    """删除备份"""
    try:
        backup_path = BACKUP_FOLDER / backup_filename
        info_path = backup_path.with_suffix('.json')
        
        if backup_path.exists():
            backup_path.unlink()
        if info_path.exists():
            info_path.unlink()
        
        return {"success": True, "message": "备份删除成功"}
    except Exception as e:
        logger.error(f"删除备份失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除备份失败: {str(e)}")

@router.post("/export")
async def export_settings(request: ExportRequest):
    """导出设置"""
    try:
        settings = load_settings()

        # 如果指定了特定部分，只导出这些部分
        if request.sections:
            export_data = {}
            for section in request.sections:
                if section in settings:
                    export_data[section] = settings[section]
        else:
            export_data = settings

        # 添加元数据
        export_package = {
            "version": "1.0",
            "export_time": datetime.now().isoformat(),
            "format": request.format,
            "data": export_data
        }

        if request.format == "yaml":
            import yaml
            content = yaml.dump(export_package, default_flow_style=False, allow_unicode=True)
            media_type = "application/x-yaml"
            filename = f"aihawk-settings-{datetime.now().strftime('%Y%m%d_%H%M%S')}.yaml"
        elif request.format == "csv":
            # CSV格式比较复杂，这里简化处理
            import csv
            import io
            output = io.StringIO()
            writer = csv.writer(output)
            writer.writerow(["Section", "Key", "Value"])

            def flatten_dict(d, parent_key='', sep='.'):
                items = []
                for k, v in d.items():
                    new_key = f"{parent_key}{sep}{k}" if parent_key else k
                    if isinstance(v, dict):
                        items.extend(flatten_dict(v, new_key, sep=sep).items())
                    else:
                        items.append((new_key, v))
                return dict(items)

            flat_data = flatten_dict(export_data)
            for key, value in flat_data.items():
                writer.writerow([key.split('.')[0], key, str(value)])

            content = output.getvalue()
            media_type = "text/csv"
            filename = f"aihawk-settings-{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        else:  # JSON
            import json
            content = json.dumps(export_package, indent=2, ensure_ascii=False)
            media_type = "application/json"
            filename = f"aihawk-settings-{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        from fastapi.responses import Response
        return Response(
            content=content,
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    except Exception as e:
        logger.error(f"导出设置失败: {e}")
        raise HTTPException(status_code=500, detail=f"导出设置失败: {str(e)}")

@router.post("/import")
async def import_settings(file: UploadFile = File(...)):
    """导入设置"""
    try:
        # 创建备份
        create_backup("auto_backup_before_import")

        content = await file.read()

        if file.filename.endswith('.json'):
            import json
            import_data = json.loads(content.decode('utf-8'))
        elif file.filename.endswith('.yaml') or file.filename.endswith('.yml'):
            import yaml
            import_data = yaml.safe_load(content.decode('utf-8'))
        else:
            raise HTTPException(status_code=400, detail="不支持的文件格式，请使用JSON或YAML文件")

        # 验证导入数据格式
        if isinstance(import_data, dict) and 'data' in import_data:
            settings_data = import_data['data']
        elif isinstance(import_data, dict):
            settings_data = import_data
        else:
            raise HTTPException(status_code=400, detail="无效的设置文件格式")

        # 合并设置
        current_settings = load_settings()
        updated_settings = merge_settings(current_settings, settings_data)

        # 保存设置
        success = save_settings(updated_settings)
        if success:
            return {"success": True, "message": "设置导入成功"}
        else:
            raise HTTPException(status_code=500, detail="设置保存失败")
    except Exception as e:
        logger.error(f"导入设置失败: {e}")
        raise HTTPException(status_code=500, detail=f"导入设置失败: {str(e)}")

@router.post("/reset")
async def reset_settings(section: Optional[str] = None):
    """重置设置"""
    try:
        # 创建备份
        create_backup("auto_backup_before_reset")

        if section:
            # 重置特定部分
            current_settings = load_settings()
            if section in DEFAULT_SETTINGS:
                current_settings[section] = DEFAULT_SETTINGS[section].copy()
                save_settings(current_settings)
            else:
                raise HTTPException(status_code=400, detail=f"未知的设置部分: {section}")
        else:
            # 重置所有设置
            save_settings(DEFAULT_SETTINGS.copy())

        return {"success": True, "message": "设置重置成功"}
    except Exception as e:
        logger.error(f"重置设置失败: {e}")
        raise HTTPException(status_code=500, detail=f"重置设置失败: {str(e)}")

@router.delete("/clear-cache")
async def clear_cache():
    """清除缓存"""
    try:
        # 这里可以添加清除各种缓存的逻辑
        # 例如：清除临时文件、清除数据库缓存等

        # 清除备份文件夹中的临时文件
        import glob
        temp_files = glob.glob(str(BACKUP_FOLDER / "temp_*"))
        for temp_file in temp_files:
            try:
                os.unlink(temp_file)
            except Exception:
                pass

        return {"success": True, "message": "缓存清除成功"}
    except Exception as e:
        logger.error(f"清除缓存失败: {e}")
        raise HTTPException(status_code=500, detail=f"清除缓存失败: {str(e)}")
