#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
跨平台浏览器检测工具
支持自动检测Chrome、Chromium等浏览器的安装路径
"""

import os
import platform
import subprocess
import shutil
from pathlib import Path
from typing import Optional, List, Dict
import logging

logger = logging.getLogger(__name__)


class BrowserDetector:
    """跨平台浏览器检测器"""
    
    def __init__(self):
        self.system = platform.system().lower()
        self.chrome_paths = self._get_chrome_search_paths()
        self.chromium_paths = self._get_chromium_search_paths()
    
    def _get_chrome_search_paths(self) -> List[str]:
        """获取Chrome可能的安装路径"""
        if self.system == "windows":
            return [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe"),
                os.path.expanduser(r"~\AppData\Roaming\Google\Chrome\Application\chrome.exe"),
            ]
        elif self.system == "darwin":  # macOS
            return [
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                os.path.expanduser("~/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"),
                "/usr/bin/google-chrome",
                "/usr/local/bin/google-chrome",
            ]
        elif self.system == "linux":
            return [
                "/usr/bin/google-chrome",
                "/usr/bin/google-chrome-stable",
                "/usr/bin/chromium-browser",
                "/usr/local/bin/google-chrome",
                "/opt/google/chrome/chrome",
                "/snap/bin/chromium",
                os.path.expanduser("~/.local/bin/google-chrome"),
            ]
        else:
            return []
    
    def _get_chromium_search_paths(self) -> List[str]:
        """获取Chromium可能的安装路径"""
        if self.system == "windows":
            return [
                r"C:\Program Files\Chromium\Application\chrome.exe",
                r"C:\Program Files (x86)\Chromium\Application\chrome.exe",
                os.path.expanduser(r"~\AppData\Local\Chromium\Application\chrome.exe"),
            ]
        elif self.system == "darwin":  # macOS
            return [
                "/Applications/Chromium.app/Contents/MacOS/Chromium",
                os.path.expanduser("~/Applications/Chromium.app/Contents/MacOS/Chromium"),
                "/usr/bin/chromium",
                "/usr/local/bin/chromium",
            ]
        elif self.system == "linux":
            return [
                "/usr/bin/chromium",
                "/usr/bin/chromium-browser",
                "/usr/local/bin/chromium",
                "/snap/bin/chromium",
                "/opt/chromium/chromium",
                os.path.expanduser("~/.local/bin/chromium"),
            ]
        else:
            return []
    
    def find_chrome_executable(self) -> Optional[str]:
        """查找Chrome可执行文件路径"""
        logger.info(f"开始在 {self.system} 系统上查找Chrome浏览器...")
        
        # 首先尝试使用which/where命令查找
        chrome_from_path = self._find_chrome_from_path()
        if chrome_from_path:
            logger.info(f"通过PATH环境变量找到Chrome: {chrome_from_path}")
            return chrome_from_path
        
        # 然后检查常见安装路径
        for path in self.chrome_paths:
            if os.path.isfile(path) and os.access(path, os.X_OK):
                logger.info(f"找到Chrome可执行文件: {path}")
                return path
        
        # 最后尝试Chromium作为备选
        for path in self.chromium_paths:
            if os.path.isfile(path) and os.access(path, os.X_OK):
                logger.info(f"找到Chromium可执行文件作为备选: {path}")
                return path
        
        logger.warning("未找到Chrome或Chromium可执行文件")
        return None
    
    def _find_chrome_from_path(self) -> Optional[str]:
        """通过PATH环境变量查找Chrome"""
        chrome_names = []
        
        if self.system == "windows":
            chrome_names = ["chrome.exe", "google-chrome.exe", "chromium.exe"]
        else:
            chrome_names = ["google-chrome", "google-chrome-stable", "chromium", "chromium-browser"]
        
        for name in chrome_names:
            path = shutil.which(name)
            if path:
                return path
        
        return None
    
    def verify_chrome_installation(self, chrome_path: str) -> Dict[str, any]:
        """验证Chrome安装并获取版本信息"""
        if not chrome_path or not os.path.isfile(chrome_path):
            return {
                "valid": False,
                "error": f"Chrome可执行文件不存在: {chrome_path}"
            }
        
        if not os.access(chrome_path, os.X_OK):
            return {
                "valid": False,
                "error": f"Chrome可执行文件没有执行权限: {chrome_path}"
            }
        
        try:
            # 获取Chrome版本 - 使用更短的超时时间和额外参数
            version_cmd = [chrome_path, "--version", "--no-sandbox", "--disable-gpu"]
            result = subprocess.run(
                version_cmd,
                capture_output=True,
                text=True,
                timeout=5,  # 减少超时时间
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0  # Windows下不显示窗口
            )
            
            if result.returncode == 0:
                version = result.stdout.strip()
                logger.info(f"Chrome版本验证成功: {version}")
                return {
                    "valid": True,
                    "version": version,
                    "path": chrome_path
                }
            else:
                return {
                    "valid": False,
                    "error": f"无法获取Chrome版本，返回码: {result.returncode}"
                }
        
        except subprocess.TimeoutExpired:
            # 版本检查超时，但文件存在且可执行，认为是可用的
            logger.warning("Chrome版本检查超时，但文件可执行，继续使用")
            return {
                "valid": True,
                "version": "版本检查超时，但可执行",
                "path": chrome_path,
                "warning": "版本检查超时"
            }
        except Exception as e:
            # 其他错误也尝试继续使用
            logger.warning(f"Chrome版本检查失败: {e}，但文件可执行，继续使用")
            return {
                "valid": True,
                "version": "版本检查失败，但可执行",
                "path": chrome_path,
                "warning": f"版本检查失败: {str(e)}"
            }
    
    def get_browser_info(self) -> Dict[str, any]:
        """获取完整的浏览器信息"""
        chrome_path = self.find_chrome_executable()
        
        if not chrome_path:
            return {
                "found": False,
                "error": "未找到Chrome或Chromium浏览器",
                "suggestions": self._get_installation_suggestions()
            }
        
        verification = self.verify_chrome_installation(chrome_path)
        
        return {
            "found": True,
            "path": chrome_path,
            "verification": verification,
            "system": self.system,
            "search_paths": {
                "chrome": self.chrome_paths,
                "chromium": self.chromium_paths
            }
        }
    
    def _get_installation_suggestions(self) -> List[str]:
        """获取浏览器安装建议"""
        if self.system == "windows":
            return [
                "请从 https://www.google.com/chrome/ 下载并安装Chrome浏览器",
                "或者安装Chromium浏览器作为替代方案",
                "确保安装到默认路径以便自动检测"
            ]
        elif self.system == "darwin":
            return [
                "请从 https://www.google.com/chrome/ 下载并安装Chrome浏览器",
                "或者使用Homebrew安装: brew install --cask google-chrome",
                "或者安装Chromium: brew install --cask chromium"
            ]
        elif self.system == "linux":
            return [
                "Ubuntu/Debian: sudo apt-get install google-chrome-stable",
                "CentOS/RHEL: sudo yum install google-chrome-stable",
                "或者安装Chromium: sudo apt-get install chromium-browser",
                "或者从 https://www.google.com/chrome/ 下载deb/rpm包安装"
            ]
        else:
            return ["请安装Chrome或Chromium浏览器"]


# 全局实例
browser_detector = BrowserDetector()


def find_chrome_executable() -> Optional[str]:
    """便捷函数：查找Chrome可执行文件"""
    return browser_detector.find_chrome_executable()


def get_browser_info() -> Dict[str, any]:
    """便捷函数：获取浏览器信息"""
    return browser_detector.get_browser_info()


def verify_chrome_installation(chrome_path: str) -> Dict[str, any]:
    """便捷函数：验证Chrome安装"""
    return browser_detector.verify_chrome_installation(chrome_path)


if __name__ == "__main__":
    # 测试浏览器检测
    print("=== 浏览器检测测试 ===")
    
    info = get_browser_info()
    print(f"检测结果: {info}")
    
    if info["found"]:
        print(f"✓ 找到浏览器: {info['path']}")
        if info["verification"]["valid"]:
            print(f"✓ 版本验证成功: {info['verification']['version']}")
        else:
            print(f"✗ 版本验证失败: {info['verification']['error']}")
    else:
        print(f"✗ 未找到浏览器: {info['error']}")
        print("安装建议:")
        for suggestion in info["suggestions"]:
            print(f"  - {suggestion}")
