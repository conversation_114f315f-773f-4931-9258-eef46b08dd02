import React, { createContext, useContext, useState, useEffect } from 'react';

// 创建LinkedIn状态上下文
const LinkedInContext = createContext();

// LinkedIn状态提供者组件
export const LinkedInProvider = ({ children }) => {
  // LinkedIn搜索结果状态
  const [searchResults, setSearchResults] = useState([]);
  const [selectedJobs, setSelectedJobs] = useState(new Set());
  const [searchForm, setSearchForm] = useState({
    keywords: '',
    location: '',
    easy_apply_only: true
  });
  
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  
  // 自动化状态
  const [automationStatus, setAutomationStatus] = useState({
    is_running: false,
    is_logged_in: false,
    current_task: null,
    automation_type: 'selenium',
    progress: {
      total_found: 0,
      total_applied: 0,
      successful_applications: [],
      failed_applications: []
    }
  });
  
  // 批量申请设置
  const [batchSettings, setBatchSettings] = useState({
    max_applications: 10,
    keywords: [],
    location: ''
  });

  // 从sessionStorage恢复状态
  useEffect(() => {
    try {
      const savedSearchResults = sessionStorage.getItem('linkedin_searchResults');
      const savedSelectedJobs = sessionStorage.getItem('linkedin_selectedJobs');
      const savedSearchForm = sessionStorage.getItem('linkedin_searchForm');
      const savedCurrentPage = sessionStorage.getItem('linkedin_currentPage');
      const savedItemsPerPage = sessionStorage.getItem('linkedin_itemsPerPage');
      const savedBatchSettings = sessionStorage.getItem('linkedin_batchSettings');

      if (savedSearchResults) {
        setSearchResults(JSON.parse(savedSearchResults));
      }

      if (savedSelectedJobs) {
        setSelectedJobs(new Set(JSON.parse(savedSelectedJobs)));
      }

      if (savedSearchForm) {
        setSearchForm(JSON.parse(savedSearchForm));
      }

      if (savedCurrentPage) {
        setCurrentPage(parseInt(savedCurrentPage));
      }

      if (savedItemsPerPage) {
        setItemsPerPage(parseInt(savedItemsPerPage));
      }

      if (savedBatchSettings) {
        setBatchSettings(JSON.parse(savedBatchSettings));
      }
    } catch (error) {
      console.error('恢复LinkedIn状态失败:', error);
    }
  }, []);

  // 保存状态到sessionStorage
  const saveToSessionStorage = (key, value) => {
    try {
      if (value instanceof Set) {
        sessionStorage.setItem(key, JSON.stringify([...value]));
      } else {
        sessionStorage.setItem(key, JSON.stringify(value));
      }
    } catch (error) {
      console.error(`保存${key}到sessionStorage失败:`, error);
    }
  };

  // 更新搜索结果
  const updateSearchResults = (results) => {
    setSearchResults(results);
    setCurrentPage(1); // 重置到第一页
    setSelectedJobs(new Set()); // 清空选择
    saveToSessionStorage('linkedin_searchResults', results);
    saveToSessionStorage('linkedin_selectedJobs', []);
    saveToSessionStorage('linkedin_currentPage', 1);
  };

  // 更新选中的职位
  const updateSelectedJobs = (newSelectedJobs) => {
    setSelectedJobs(newSelectedJobs);
    saveToSessionStorage('linkedin_selectedJobs', [...newSelectedJobs]);
  };

  // 更新搜索表单
  const updateSearchForm = (newForm) => {
    setSearchForm(newForm);
    saveToSessionStorage('linkedin_searchForm', newForm);
  };

  // 更新当前页
  const updateCurrentPage = (page) => {
    setCurrentPage(page);
    saveToSessionStorage('linkedin_currentPage', page);
  };

  // 更新每页显示数量
  const updateItemsPerPage = (count) => {
    setItemsPerPage(count);
    setCurrentPage(1); // 重置到第一页
    saveToSessionStorage('linkedin_itemsPerPage', count);
    saveToSessionStorage('linkedin_currentPage', 1);
  };

  // 更新批量设置
  const updateBatchSettings = (newSettings) => {
    setBatchSettings(newSettings);
    saveToSessionStorage('linkedin_batchSettings', newSettings);
  };

  // 清除所有LinkedIn数据
  const clearLinkedInData = () => {
    setSearchResults([]);
    setSelectedJobs(new Set());
    setCurrentPage(1);
    setItemsPerPage(10);
    setSearchForm({
      keywords: '',
      location: '',
      easy_apply_only: true
    });
    setBatchSettings({
      max_applications: 10,
      keywords: [],
      location: ''
    });

    // 清除sessionStorage
    sessionStorage.removeItem('linkedin_searchResults');
    sessionStorage.removeItem('linkedin_selectedJobs');
    sessionStorage.removeItem('linkedin_searchForm');
    sessionStorage.removeItem('linkedin_currentPage');
    sessionStorage.removeItem('linkedin_itemsPerPage');
    sessionStorage.removeItem('linkedin_batchSettings');
  };

  // 分页计算
  const totalPages = Math.ceil(searchResults.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentPageJobs = searchResults.slice(startIndex, endIndex);

  // 选择逻辑（基于当前页面）
  const isAllSelected = currentPageJobs.length > 0 && currentPageJobs.every(job => selectedJobs.has(job.job_id));
  const isIndeterminate = currentPageJobs.some(job => selectedJobs.has(job.job_id)) && !isAllSelected;

  const contextValue = {
    // 状态
    searchResults,
    selectedJobs,
    searchForm,
    currentPage,
    itemsPerPage,
    automationStatus,
    batchSettings,
    
    // 分页计算
    totalPages,
    startIndex,
    endIndex,
    currentPageJobs,
    isAllSelected,
    isIndeterminate,
    
    // 更新函数
    updateSearchResults,
    updateSelectedJobs,
    updateSearchForm,
    updateCurrentPage,
    updateItemsPerPage,
    setAutomationStatus,
    updateBatchSettings,
    clearLinkedInData
  };

  return (
    <LinkedInContext.Provider value={contextValue}>
      {children}
    </LinkedInContext.Provider>
  );
};

// 自定义Hook来使用LinkedIn上下文
export const useLinkedIn = () => {
  const context = useContext(LinkedInContext);
  if (!context) {
    throw new Error('useLinkedIn must be used within a LinkedInProvider');
  }
  return context;
};

export { LinkedInContext as default };
