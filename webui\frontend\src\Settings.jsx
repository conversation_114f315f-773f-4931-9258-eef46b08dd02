import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Card,
  Alert,
  Tabs,
  Tab,
  CircularProgress
} from '@mui/material';
import {
  LinkedIn as LinkedInIcon,
  Storage as DataIcon,
  Settings as SystemIcon,
  TuneRounded as AdvancedIcon
} from '@mui/icons-material';

// 简化的设置上下文
const useSimpleSettings = () => {
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    setTimeout(() => setIsLoading(false), 1000);
  }, []);
  
  return { isLoading };
};

function TabPanel({ children, value, index }) {
  return (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

function Settings() {
  const { isLoading } = useSimpleSettings();
  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (_, newValue) => {
    setActiveTab(newValue);
  };

  if (isLoading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
        <CircularProgress size={60} />
      </Container>
    );
  }

  const tabs = [
    { label: 'LinkedIn自动化', icon: <LinkedInIcon /> },
    { label: '数据管理', icon: <DataIcon /> },
    { label: '系统设置', icon: <SystemIcon /> },
    { label: '高级功能', icon: <AdvancedIcon /> }
  ];

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h2" sx={{ mb: 4, color: 'text.primary' }}>
        设置
      </Typography>
      
      <Card sx={{ bgcolor: 'background.paper', borderRadius: 2 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
          >
            {tabs.map((tab, index) => (
              <Tab
                key={index}
                icon={tab.icon}
                label={tab.label}
                iconPosition="start"
                sx={{
                  display: 'flex',
                  flexDirection: 'row',
                  gap: 1,
                  minWidth: 160
                }}
              />
            ))}
          </Tabs>
        </Box>

        {tabs.map((tab, index) => (
          <TabPanel key={index} value={activeTab} index={index}>
            <Typography variant="h6" sx={{ mb: 2 }}>
              {tab.label}
            </Typography>
            <Alert severity="info">
              {tab.label}功能正在开发中，即将上线...
            </Alert>
          </TabPanel>
        ))}
      </Card>
    </Container>
  );
}

export default Settings;
