import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  TextField,
  Slider,
  Divider,
  Grid,
  Chip,
  Button,
  Alert
} from '@mui/material';
import {
  LinkedIn as LinkedInIcon,
  Computer as BrowserIcon,
  Speed as SpeedIcon,
  Message as MessageIcon
} from '@mui/icons-material';
import { useSettings } from '../SettingsContext';

function LinkedInSettings({ onReset }) {
  const { settings, updateSettings, getSetting } = useSettings();

  const handleChange = (path, value) => {
    updateSettings(path, value);
  };

  const automationTypes = [
    { value: 'selenium', label: 'Selenium (推荐)', description: '稳定可靠，兼容性好' },
    // 暂时屏蔽Playwright选项，保留代码但注释掉
    // { value: 'playwright', label: 'Playwright 同步', description: '现代化，功能丰富' },
    // { value: 'playwright_async', label: 'Playwright 异步', description: '高性能，适合批量操作' }
  ];

  const creativityLevels = [
    { value: 'conservative', label: '保守', description: '严格按照模板' },
    { value: 'balanced', label: '平衡', description: '适度创新' },
    { value: 'creative', label: '创新', description: '更多个性化内容' }
  ];

  return (
    <Box>
      <Typography variant="h5" sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
        <LinkedInIcon sx={{ mr: 2, color: 'primary.main' }} />
        LinkedIn自动化设置
      </Typography>

      {/* 自动化模式选择 */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
              <LinkedInIcon sx={{ mr: 1, color: 'secondary.main' }} />
              自动化模式
            </Typography>
            <Button
              variant="outlined"
              size="small"
              onClick={() => onReset('linkedin')}
              sx={{ color: 'text.secondary' }}
            >
              重置此部分
            </Button>
          </Box>

          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>自动化类型</InputLabel>
            <Select
              value={getSetting('linkedin.automation_type') || 'selenium'}
              onChange={(e) => handleChange('linkedin.automation_type', e.target.value)}
              label="自动化类型"
            >
              {automationTypes.map((type) => (
                <MenuItem key={type.value} value={type.value}>
                  <Box>
                    <Typography variant="body1">{type.label}</Typography>
                    <Typography variant="caption" color="text.secondary">
                      {type.description}
                    </Typography>
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <Alert severity="info" sx={{ mt: 2 }}>
            当前版本暂时只支持Selenium自动化方式，Playwright选项因异步/同步问题暂时屏蔽。Selenium是最稳定的选择，推荐用于日常使用。
          </Alert>
        </CardContent>
      </Card>

      {/* 浏览器配置 */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
            <BrowserIcon sx={{ mr: 1, color: 'secondary.main' }} />
            浏览器配置
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={getSetting('linkedin.browser.headless') || false}
                    onChange={(e) => handleChange('linkedin.browser.headless', e.target.checked)}
                  />
                }
                label="无头模式"
              />
              <Typography variant="caption" display="block" color="text.secondary">
                启用后浏览器将在后台运行，不显示界面
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography gutterBottom>超时时间 (秒)</Typography>
              <TextField
                type="number"
                value={getSetting('linkedin.browser.timeout') / 1000 || 30}
                onChange={(e) => handleChange('linkedin.browser.timeout', parseInt(e.target.value) * 1000)}
                inputProps={{ min: 10, max: 120 }}
                fullWidth
                size="small"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography gutterBottom>重试次数</Typography>
              <TextField
                type="number"
                value={getSetting('linkedin.browser.retry_count') || 3}
                onChange={(e) => handleChange('linkedin.browser.retry_count', parseInt(e.target.value))}
                inputProps={{ min: 1, max: 10 }}
                fullWidth
                size="small"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography gutterBottom>窗口大小</Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <TextField
                  label="宽度"
                  type="number"
                  value={getSetting('linkedin.browser.window_size')?.[0] || 1920}
                  onChange={(e) => {
                    const currentSize = getSetting('linkedin.browser.window_size') || [1920, 1080];
                    handleChange('linkedin.browser.window_size', [parseInt(e.target.value), currentSize[1]]);
                  }}
                  size="small"
                  sx={{ flex: 1 }}
                />
                <TextField
                  label="高度"
                  type="number"
                  value={getSetting('linkedin.browser.window_size')?.[1] || 1080}
                  onChange={(e) => {
                    const currentSize = getSetting('linkedin.browser.window_size') || [1920, 1080];
                    handleChange('linkedin.browser.window_size', [currentSize[0], parseInt(e.target.value)]);
                  }}
                  size="small"
                  sx={{ flex: 1 }}
                />
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* 申请频率限制 */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
            <SpeedIcon sx={{ mr: 1, color: 'secondary.main' }} />
            申请频率限制
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography gutterBottom>每日最大申请数量</Typography>
              <Slider
                value={getSetting('linkedin.application.max_applications_per_day') || 50}
                onChange={(e, value) => handleChange('linkedin.application.max_applications_per_day', value)}
                min={1}
                max={100}
                marks={[
                  { value: 10, label: '10' },
                  { value: 50, label: '50' },
                  { value: 100, label: '100' }
                ]}
                valueLabelDisplay="on"
              />
              <Typography variant="caption" color="text.secondary">
                建议不超过50个，避免账号被限制
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography gutterBottom>申请间隔时间 (秒)</Typography>
              <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                <TextField
                  label="最小间隔"
                  type="number"
                  value={getSetting('linkedin.application.delay_between_applications')?.[0] || 30}
                  onChange={(e) => {
                    const currentDelay = getSetting('linkedin.application.delay_between_applications') || [30, 60];
                    handleChange('linkedin.application.delay_between_applications', [parseInt(e.target.value), currentDelay[1]]);
                  }}
                  size="small"
                  sx={{ flex: 1 }}
                />
                <Typography>-</Typography>
                <TextField
                  label="最大间隔"
                  type="number"
                  value={getSetting('linkedin.application.delay_between_applications')?.[1] || 60}
                  onChange={(e) => {
                    const currentDelay = getSetting('linkedin.application.delay_between_applications') || [30, 60];
                    handleChange('linkedin.application.delay_between_applications', [currentDelay[0], parseInt(e.target.value)]);
                  }}
                  size="small"
                  sx={{ flex: 1 }}
                />
              </Box>
              <Typography variant="caption" color="text.secondary">
                随机间隔时间，模拟人工操作
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={getSetting('linkedin.application.auto_answer_questions') || true}
                    onChange={(e) => handleChange('linkedin.application.auto_answer_questions', e.target.checked)}
                  />
                }
                label="自动回答常见问题"
              />
              <Typography variant="caption" display="block" color="text.secondary">
                启用后将自动填写工作经验、授权等常见问题
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* 自动回复模板 */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
            <MessageIcon sx={{ mr: 1, color: 'secondary.main' }} />
            自动回复模板
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="工作经验年数"
                value={getSetting('linkedin.application.default_answers.years_experience') || '3'}
                onChange={(e) => handleChange('linkedin.application.default_answers.years_experience', e.target.value)}
                size="small"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth size="small">
                <InputLabel>是否愿意搬迁</InputLabel>
                <Select
                  value={getSetting('linkedin.application.default_answers.willing_to_relocate') || 'Yes'}
                  onChange={(e) => handleChange('linkedin.application.default_answers.willing_to_relocate', e.target.value)}
                  label="是否愿意搬迁"
                >
                  <MenuItem value="Yes">是</MenuItem>
                  <MenuItem value="No">否</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth size="small">
                <InputLabel>是否有工作授权</InputLabel>
                <Select
                  value={getSetting('linkedin.application.default_answers.authorized_to_work') || 'Yes'}
                  onChange={(e) => handleChange('linkedin.application.default_answers.authorized_to_work', e.target.value)}
                  label="是否有工作授权"
                >
                  <MenuItem value="Yes">是</MenuItem>
                  <MenuItem value="No">否</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth size="small">
                <InputLabel>是否需要签证赞助</InputLabel>
                <Select
                  value={getSetting('linkedin.application.default_answers.require_sponsorship') || 'No'}
                  onChange={(e) => handleChange('linkedin.application.default_answers.require_sponsorship', e.target.value)}
                  label="是否需要签证赞助"
                >
                  <MenuItem value="Yes">是</MenuItem>
                  <MenuItem value="No">否</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="求职信模板"
                placeholder="输入自定义求职信模板..."
                value={getSetting('linkedin.templates.cover_letter_template') || ''}
                onChange={(e) => handleChange('linkedin.templates.cover_letter_template', e.target.value)}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                multiline
                rows={2}
                label="跟进消息模板"
                placeholder="感谢您考虑我的申请..."
                value={getSetting('linkedin.templates.follow_up_message') || ''}
                onChange={(e) => handleChange('linkedin.templates.follow_up_message', e.target.value)}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                multiline
                rows={2}
                label="感谢消息模板"
                placeholder="感谢您的时间和考虑..."
                value={getSetting('linkedin.templates.thank_you_message') || ''}
                onChange={(e) => handleChange('linkedin.templates.thank_you_message', e.target.value)}
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
}

export default LinkedInSettings;
