import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Slider,
  Button,
  Grid,
  Alert,
  Divider
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Palette as ThemeIcon,
  Language as LanguageIcon,
  TextFields as FontIcon,
  Notifications as NotificationIcon
} from '@mui/icons-material';
import { useSettings } from '../SettingsContext';

function SystemSettings({ onReset }) {
  const { settings, updateSettings, getSetting } = useSettings();

  const handleChange = (path, value) => {
    updateSettings(path, value);
  };

  const themes = [
    { value: 'dark', label: '深色主题', description: '适合长时间使用，保护视力' },
    { value: 'light', label: '浅色主题', description: '经典明亮界面' }
  ];

  const languages = [
    { value: 'zh', label: '中文', description: '简体中文界面' },
    { value: 'en', label: 'English', description: 'English interface' }
  ];

  const fontSizes = [
    { value: 'small', label: '小', description: '紧凑显示' },
    { value: 'medium', label: '中', description: '标准大小' },
    { value: 'large', label: '大', description: '易于阅读' }
  ];

  // 注意：实际的主题切换需要在App.jsx中实现
  const handleThemeChange = (theme) => {
    handleChange('system.theme', theme);
    // 这里可以添加实际的主题切换逻辑
    console.log('主题切换到:', theme);
  };

  // 语言切换处理
  const handleLanguageChange = (language) => {
    handleChange('system.language', language);
    // 这里可以添加实际的语言切换逻辑
    console.log('语言切换到:', language);
  };

  return (
    <Box>
      <Typography variant="h5" sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
        <SettingsIcon sx={{ mr: 2, color: 'primary.main' }} />
        系统设置
      </Typography>

      {/* 界面主题 */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
              <ThemeIcon sx={{ mr: 1, color: 'secondary.main' }} />
              界面主题
            </Typography>
            <Button
              variant="outlined"
              size="small"
              onClick={() => onReset('system')}
              sx={{ color: 'text.secondary' }}
            >
              重置此部分
            </Button>
          </Box>

          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>主题选择</InputLabel>
            <Select
              value={getSetting('system.theme') || 'dark'}
              onChange={(e) => handleThemeChange(e.target.value)}
              label="主题选择"
            >
              {themes.map((theme) => (
                <MenuItem key={theme.value} value={theme.value}>
                  <Box>
                    <Typography variant="body1">{theme.label}</Typography>
                    <Typography variant="caption" color="text.secondary">
                      {theme.description}
                    </Typography>
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <Alert severity="info">
            主题更改将在下次刷新页面时生效。当前项目使用深色主题作为默认设计。
          </Alert>
        </CardContent>
      </Card>

      {/* 语言设置 */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
            <LanguageIcon sx={{ mr: 1, color: 'secondary.main' }} />
            语言偏好
          </Typography>

          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>界面语言</InputLabel>
            <Select
              value={getSetting('system.language') || 'zh'}
              onChange={(e) => handleLanguageChange(e.target.value)}
              label="界面语言"
            >
              {languages.map((lang) => (
                <MenuItem key={lang.value} value={lang.value}>
                  <Box>
                    <Typography variant="body1">{lang.label}</Typography>
                    <Typography variant="caption" color="text.secondary">
                      {lang.description}
                    </Typography>
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <Alert severity="warning">
            注意：语言切换功能需要谨慎实现，确保不破坏现有项目结构和功能。当前仅保存偏好设置。
          </Alert>
        </CardContent>
      </Card>

      {/* 字体设置 */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
            <FontIcon sx={{ mr: 1, color: 'secondary.main' }} />
            字体设置
          </Typography>

          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>字体大小</InputLabel>
            <Select
              value={getSetting('system.font_size') || 'medium'}
              onChange={(e) => handleChange('system.font_size', e.target.value)}
              label="字体大小"
            >
              {fontSizes.map((size) => (
                <MenuItem key={size.value} value={size.value}>
                  <Box>
                    <Typography variant="body1">{size.label}</Typography>
                    <Typography variant="caption" color="text.secondary">
                      {size.description}
                    </Typography>
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <Typography variant="body2" color="text.secondary">
            字体大小调整将影响整个应用的文字显示效果
          </Typography>
        </CardContent>
      </Card>

      {/* 通知设置 */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
            <NotificationIcon sx={{ mr: 1, color: 'secondary.main' }} />
            通知设置
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <FormControlLabel
                control={
                  <Switch
                    checked={getSetting('system.notifications.desktop') || true}
                    onChange={(e) => handleChange('system.notifications.desktop', e.target.checked)}
                  />
                }
                label="桌面通知"
              />
              <Typography variant="caption" display="block" color="text.secondary">
                显示系统桌面通知
              </Typography>
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControlLabel
                control={
                  <Switch
                    checked={getSetting('system.notifications.sound') || true}
                    onChange={(e) => handleChange('system.notifications.sound', e.target.checked)}
                  />
                }
                label="声音提醒"
              />
              <Typography variant="caption" display="block" color="text.secondary">
                播放通知声音
              </Typography>
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControlLabel
                control={
                  <Switch
                    checked={getSetting('system.notifications.email') || false}
                    onChange={(e) => handleChange('system.notifications.email', e.target.checked)}
                  />
                }
                label="邮件通知"
              />
              <Typography variant="caption" display="block" color="text.secondary">
                发送邮件通知
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* 其他系统设置 */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>其他设置</Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={getSetting('system.auto_save') || true}
                    onChange={(e) => handleChange('system.auto_save', e.target.checked)}
                  />
                }
                label="自动保存"
              />
              <Typography variant="caption" display="block" color="text.secondary">
                自动保存设置更改，无需手动保存
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={getSetting('system.debug_mode') || false}
                    onChange={(e) => handleChange('system.debug_mode', e.target.checked)}
                  />
                }
                label="调试模式"
              />
              <Typography variant="caption" display="block" color="text.secondary">
                启用详细日志和调试信息
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="subtitle2" sx={{ mb: 2 }}>性能设置</Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={getSetting('advanced.performance.cache_enabled') || true}
                        onChange={(e) => handleChange('advanced.performance.cache_enabled', e.target.checked)}
                      />
                    }
                    label="启用缓存"
                  />
                  <Typography variant="caption" display="block" color="text.secondary">
                    缓存数据以提高性能
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={getSetting('advanced.performance.preload_data') || true}
                        onChange={(e) => handleChange('advanced.performance.preload_data', e.target.checked)}
                      />
                    }
                    label="预加载数据"
                  />
                  <Typography variant="caption" display="block" color="text.secondary">
                    预先加载常用数据
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={getSetting('advanced.performance.lazy_loading') || true}
                        onChange={(e) => handleChange('advanced.performance.lazy_loading', e.target.checked)}
                      />
                    }
                    label="懒加载"
                  />
                  <Typography variant="caption" display="block" color="text.secondary">
                    按需加载内容以节省资源
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography gutterBottom>缓存持续时间 (分钟)</Typography>
                  <Slider
                    value={(getSetting('advanced.performance.cache_duration') || 3600000) / 60000}
                    onChange={(e, value) => handleChange('advanced.performance.cache_duration', value * 60000)}
                    min={5}
                    max={120}
                    marks={[
                      { value: 5, label: '5分钟' },
                      { value: 60, label: '1小时' },
                      { value: 120, label: '2小时' }
                    ]}
                    valueLabelDisplay="on"
                    valueLabelFormat={(value) => `${value}分钟`}
                  />
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
}

export default SystemSettings;
