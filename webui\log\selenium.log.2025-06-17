2025-06-17 19:19:20,491 - DEBUG - POST http://localhost:52508/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'goog:chromeOptions': {'extensions': [], 'args': ['--headless', '--disable-gpu', '--start-maximized', '--no-sandbox', '--disable-dev-shm-usage', '--ignore-certificate-errors', 'window-size=1200x800', '--enable-javascript', '--enable-cookies', 'user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36']}}}}
2025-06-17 19:19:21,135 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.104","chrome":{"chromedriverVersion":"137.0.7151.70 (dfa4dc56b2abb56eb2a14cad006ea5e68c60d5de-refs/branch-heads/7151@{#1875})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir23664_527671178"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:52511"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"2b26cc5a204374f64e6790500d7670d2"}} | headers=HTTPHeaderDict({'Content-Length': '882', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-17 19:19:21,154 - DEBUG - Finished Request
2025-06-17 19:22:16,239 - DEBUG - POST http://localhost:52787/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'goog:chromeOptions': {'extensions': [], 'args': ['--headless', '--disable-gpu', '--start-maximized', '--no-sandbox', '--disable-dev-shm-usage', '--ignore-certificate-errors', 'window-size=1200x800', '--enable-javascript', '--enable-cookies', 'user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36']}}}}
2025-06-17 19:22:16,911 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.104","chrome":{"chromedriverVersion":"137.0.7151.70 (dfa4dc56b2abb56eb2a14cad006ea5e68c60d5de-refs/branch-heads/7151@{#1875})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir29028_636259152"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:52790"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"0462ad9424519ed0fcdd1bba4f722a29"}} | headers=HTTPHeaderDict({'Content-Length': '882', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-17 19:22:16,911 - DEBUG - Finished Request
2025-06-17 19:25:41,934 - DEBUG - POST http://localhost:53051/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'goog:chromeOptions': {'extensions': [], 'args': ['--headless', '--disable-gpu', '--start-maximized', '--no-sandbox', '--disable-dev-shm-usage', '--ignore-certificate-errors', 'window-size=1200x800', '--enable-javascript', '--enable-cookies', 'user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36']}}}}
2025-06-17 19:25:42,513 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.104","chrome":{"chromedriverVersion":"137.0.7151.70 (dfa4dc56b2abb56eb2a14cad006ea5e68c60d5de-refs/branch-heads/7151@{#1875})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir21240_1078378734"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:53054"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"87c3c1044d4f6d5a4cc883175bb17939"}} | headers=HTTPHeaderDict({'Content-Length': '883', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-17 19:25:42,513 - DEBUG - Finished Request
2025-06-17 19:27:05,771 - DEBUG - POST http://localhost:53199/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'goog:chromeOptions': {'extensions': [], 'args': ['--headless', '--disable-gpu', '--start-maximized', '--no-sandbox', '--disable-dev-shm-usage', '--ignore-certificate-errors', 'window-size=1200x800', '--enable-javascript', '--enable-cookies', 'user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36']}}}}
2025-06-17 19:27:06,414 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.104","chrome":{"chromedriverVersion":"137.0.7151.70 (dfa4dc56b2abb56eb2a14cad006ea5e68c60d5de-refs/branch-heads/7151@{#1875})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir22592_1660613048"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:53210"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"04217f92b3a309073e11ec26deeefcc9"}} | headers=HTTPHeaderDict({'Content-Length': '883', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-17 19:27:06,415 - DEBUG - Finished Request
2025-06-17 19:29:09,833 - DEBUG - POST http://localhost:53730/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'goog:chromeOptions': {'extensions': [], 'args': ['--headless', '--disable-gpu', '--start-maximized', '--no-sandbox', '--disable-dev-shm-usage', '--ignore-certificate-errors', 'window-size=1200x800', '--enable-javascript', '--enable-cookies', 'user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36']}}}}
2025-06-17 19:29:10,477 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.104","chrome":{"chromedriverVersion":"137.0.7151.70 (dfa4dc56b2abb56eb2a14cad006ea5e68c60d5de-refs/branch-heads/7151@{#1875})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir23196_1625274924"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:53735"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"591509fe79e54ac3d0f1a4bdfabcda1f"}} | headers=HTTPHeaderDict({'Content-Length': '883', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-17 19:29:10,477 - DEBUG - Finished Request
